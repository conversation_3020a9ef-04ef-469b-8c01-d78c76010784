import { NextResponse } from 'next/server';
import { query, get } from '@/lib/db';

/**
 * 测试订单数据查询API
 */
export async function GET() {
  try {
    console.log('开始测试订单数据查询...');

    // 1. 检查销售订单表是否存在
    const tableExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='销售订单'"
    );
    console.log('销售订单表存在检查:', tableExists);

    if (!tableExists || tableExists.length === 0) {
      return NextResponse.json({
        success: false,
        message: '销售订单表不存在',
        debug: {
          tableExists: false
        }
      });
    }

    // 2. 查询销售订单表结构
    const tableStructure = await query('PRAGMA table_info(销售订单)');
    console.log('销售订单表结构:', tableStructure);

    // 3. 查询销售订单数据
    const orderCount = await get('SELECT COUNT(*) as total FROM 销售订单');
    console.log('销售订单总数:', orderCount);

    // 4. 查询最近的几条订单数据
    const recentOrders = await query(`
      SELECT 
        编号 as id,
        订单编号 as orderNumber,
        总金额 as amount,
        状态 as status,
        创建时间 as date
      FROM 销售订单 
      ORDER BY 创建时间 DESC 
      LIMIT 5
    `);
    console.log('最近订单数据:', recentOrders);

    // 5. 测试今日统计
    const today = new Date().toISOString().split('T')[0];
    const todayStats = await get(`
      SELECT
        COUNT(*) as orderCount,
        SUM(总金额) as totalSales
      FROM 销售订单
      WHERE date(创建时间) = ?
    `, [today]);
    console.log('今日统计:', todayStats);

    return NextResponse.json({
      success: true,
      message: '订单数据查询测试成功',
      debug: {
        tableExists: true,
        tableStructure,
        orderCount: orderCount?.total || 0,
        recentOrders,
        todayStats: {
          orderCount: todayStats?.orderCount || 0,
          totalSales: todayStats?.totalSales || 0
        },
        today
      }
    });

  } catch (error) {
    console.error('测试订单数据查询失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '测试订单数据查询失败',
        error: error instanceof Error ? error.message : '未知错误',
        debug: {
          errorDetails: error
        }
      },
      { status: 500 }
    );
  }
}
